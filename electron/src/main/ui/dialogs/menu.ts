import { BrowserWindow } from 'electron';
import { Application } from '@electron/main/core/application';
import { DIALOG_MARGIN_TOP, DIALOG_MARGIN } from '@electron/renderer/constants/design';

export const showMenuDialog = (
  browserWindow: BrowserWindow,
  x: number,
  y: number,
) => {
  console.log('[MenuDialog] Showing menu dialog at position:', { x, y });

  const menuWidth = 330;
  let isInitialShow = true;

  const dialog = Application.instance.dialogs.show({
    name: 'menu',
    browserWindow,
    getBounds: () => ({
      width: menuWidth,
      height: 510,
      x: x - menuWidth + DIALOG_MARGIN,
      y: y - DIALOG_MARGIN_TOP,
    }),
    onWindowBoundsUpdate: (disposition) => {
      console.log('[MenuDialog] Window bounds updated:', disposition, 'isInitialShow:', isInitialShow);

      // 避免在初始显示时立即隐藏
      if (isInitialShow) {
        console.log('[MenuDialog] Ignoring initial bounds update');
        isInitialShow = false;
        return;
      }

      // 只在窗口移动或调整大小时隐藏菜单
      if (disposition === 'move' || disposition === 'resize') {
        console.log('[MenuDialog] Hiding dialog due to window', disposition);
        dialog.hide();
      }
    },
  });

  // 添加主窗口点击监听器来关闭菜单
  const handleMainWindowClick = (event, input) => {
    // 只处理鼠标点击事件
    if (input.type === 'mouseDown') {
      console.log('[MenuDialog] Main window mouse down detected, hiding menu');
      dialog.hide();
    }
  };

  // 监听主窗口的输入事件
  browserWindow.webContents.on('before-input-event', handleMainWindowClick);

  // 当菜单隐藏时，移除监听器
  const originalHide = dialog.hide.bind(dialog);
  dialog.hide = (...args) => {
    console.log('[MenuDialog] Removing main window click listener');
    browserWindow.webContents.removeListener('before-input-event', handleMainWindowClick);
    return originalHide(...args);
  };

  // 延迟重置初始显示标志，确保初始设置完成
  setTimeout(() => {
    isInitialShow = false;
  }, 100);

  console.log('[MenuDialog] Dialog creation result:', !!dialog);
};
