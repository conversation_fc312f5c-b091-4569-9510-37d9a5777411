import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { ICON_DROPDOWN, transparency, EASING_FUNCTION, DIALOG_EASING } from '@mario-ai/shared';

// ContextMenu 组件的 Tailwind 版本
interface ContextMenuProps {
  visible: boolean;
  children: React.ReactNode;
  style?: React.CSSProperties;
  bigger?: boolean;
  translucent?: boolean;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ 
  visible, 
  children, 
  style, 
  bigger = false,
  translucent = false 
}) => {
  const classes = cn(
    // 基础样式
    'outline-none absolute w-[150px] cursor-default z-[9999]',
    'backdrop-blur-[10px] rounded-[10px] shadow-dialog',
    // 动画和可见性
    'transition-all duration-[350ms]',
    visible ? 'opacity-100 translate-y-0 pointer-events-auto' : 'opacity-0 -translate-y-[10px] pointer-events-none',
    // 背景色 - 根据 translucent 属性
    translucent ? 'bg-mario-dropdown-translucent' : 'bg-mario-dropdown'
  );

  const dynamicStyle: React.CSSProperties = {
    padding: `${bigger ? 8 : 4}px 0`,
    transitionTimingFunction: visible ? DIALOG_EASING : 'none',
    backfaceVisibility: 'hidden',
    transform: visible ? 'translateZ(0) scale(1, 1)' : 'translateZ(0) scale(1, 1)',
    ...style
  };

  return (
    <div className={classes} style={dynamicStyle}>
      {children}
    </div>
  );
};

// ContextMenuItem 组件的 Tailwind 版本
export interface ContextMenuItemProps {
  icon?: string;
  selected?: boolean;
  bigger?: boolean;
  visible?: boolean;
  iconSize?: number;
  disabled?: boolean;
  onClick?: () => void;
  onMouseDown?: (e: React.MouseEvent<any>) => void;
  children?: React.ReactNode;
}

const ContextMenuItem: React.FC<ContextMenuItemProps> = ({
  icon,
  selected = false,
  bigger = false,
  visible = true,
  iconSize = 18,
  disabled = false,
  onClick,
  onMouseDown,
  children
}) => {
  const classes = cn(
    // 基础样式
    'relative font-normal flex items-center',
    // 尺寸和间距
    bigger ? 'text-sm py-3 px-5' : 'text-[13px] py-[10px] px-3',
    // 可见性
    visible ? 'flex' : 'hidden',
    // 禁用状态
    disabled ? 'pointer-events-none opacity-38' : 'pointer-events-auto opacity-100',
    // 选中状态背景
    selected 
      ? 'bg-black bg-opacity-10 dark:bg-white dark:bg-opacity-15'
      : 'bg-transparent',
    // Hover 效果
    !disabled && (
      selected 
        ? 'hover:bg-black hover:bg-opacity-10 dark:hover:bg-white dark:hover:bg-opacity-15'
        : 'hover:bg-black hover:bg-opacity-6 dark:hover:bg-white dark:hover:bg-opacity-8'
    )
  );

  const iconStyle: React.CSSProperties = icon ? {
    paddingLeft: `${16 + iconSize + 12}px`,
  } : {};

  const iconBeforeStyle: React.CSSProperties = icon ? {
    content: '""',
    position: 'absolute',
    left: '16px',
    width: `${iconSize}px`,
    height: `${iconSize}px`,
    backgroundImage: `url(${icon})`,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    opacity: 0.54,
    filter: 'var(--icon-filter, none)', // 通过 CSS 变量控制深色主题
    WebkitBackfaceVisibility: 'hidden',
    WebkitTransform: 'translateZ(0) scale(1.0, 1.0)',
  } : {};

  return (
    <div
      className={classes}
      style={{ ...iconStyle }}
      onClick={onClick}
      onMouseDown={onMouseDown}
    >
      {icon && (
        <div 
          className="absolute"
          style={iconBeforeStyle}
        />
      )}
      {children}
    </div>
  );
};

// Dropdown Item 组件
interface ItemProps extends ContextMenuItemProps {
  value: string;
  children?: any;
}

const Item = (props: ItemProps) => {
  return <ContextMenuItem {...props}>{props.children}</ContextMenuItem>;
};

// 主 Dropdown 组件
interface Props {
  color?: string;
  children?: any;
  defaultValue?: any;
  onChange?: (newValue?: any, oldValue?: any) => void;
  onMouseDown?: (e: React.MouseEvent<any>) => void;
  style?: any;
  priority?: number; // 添加优先级属性
}

interface State {
  expanded: boolean;
  label?: string;
  value?: string;
}

export class Dropdown extends React.PureComponent<Props, State> {
  public static Item = Item;
  private instanceId = Math.random().toString(36).substring(2, 8);

  public state: State = {
    expanded: false,
  };

  componentDidMount() {
    const { defaultValue } = this.props;

    if (defaultValue != null) {
      this.setValue(defaultValue, false);
    }
  }

  componentDidUpdate(prevProps: Props) {
    if (this.props.defaultValue !== prevProps.defaultValue) {
      this.setValue(this.props.defaultValue, false);
    }
  }

  public setValue(value: string, emitEvent = true) {
    const { onChange, children } = this.props;
    const oldValue = this.state.value;
    const el = children.find((r: any) => r.props.value === value);

    if (el) {
      this.setState({
        value,
        label: el.props.children,
      });

      if (emitEvent && onChange) {
        onChange(value, oldValue);
      }
    }
  }

  public toggleMenu(val: boolean) {
    this.setState({ expanded: val });

    requestAnimationFrame(() => {
      if (val) {
        window.addEventListener('mousedown', this.onWindowMouseDown);
      } else {
        window.removeEventListener('mousedown', this.onWindowMouseDown);
      }
    });
  }



  private onItemClick = (value: string) => () => {
    this.setValue(value);
    this.toggleMenu(false);
  };

  private onItemMouseDown = (e: React.MouseEvent<any>) => {
    e.stopPropagation();
  };

  private onMouseDown = (e: React.MouseEvent<any>) => {
    console.log(`🔍 [DROPDOWN-${this.instanceId}] onMouseDown triggered`);
    console.log(`🔍 [DROPDOWN-${this.instanceId}] Priority:`, this.props.priority);
    console.log(`🔍 [DROPDOWN-${this.instanceId}] Current expanded:`, this.state.expanded);
    console.log(`🔍 [DROPDOWN-${this.instanceId}] onChange function:`, this.props.onChange?.name || 'anonymous');
    console.log(`🔍 [DROPDOWN-${this.instanceId}] Event target:`, e.target);
    console.log(`🔍 [DROPDOWN-${this.instanceId}] Event currentTarget:`, e.currentTarget);

    e.stopPropagation();

    if (this.props.onMouseDown) this.props.onMouseDown(e);

    const { expanded } = this.state;
    this.toggleMenu(!expanded);
  };

  public onWindowMouseDown = () => {
    this.toggleMenu(false);
  };

  render() {
    const { children, style } = this.props;
    const { expanded, label, value } = this.state;

    console.log(`🔍 [DROPDOWN-${this.instanceId}] Rendering with priority:`, this.props.priority, 'onChange:', this.props.onChange?.name || 'anonymous', 'expanded:', expanded);

    // 主容器样式
    const containerClasses = cn(
      'h-8 min-w-[200px] relative rounded cursor-pointer select-none',
      'flex items-center transition-colors duration-200',
      'bg-mario-control hover:bg-mario-control-hover'
      // 移除 isolate，让所有 ContextMenu 在同一个层叠上下文中比较 z-index
    );

    // 标签样式
    const labelClasses = cn(
      'text-[13px] ml-2 pointer-events-none text-mario-control-value'
    );

    // 下拉图标样式
    const iconClasses = cn(
      'w-6 h-6 ml-auto mr-[2px] transition-transform duration-200',
      expanded ? 'rotate-180' : 'rotate-0'
    );

    const iconStyle: React.CSSProperties = {
      opacity: transparency.icons.inactive,
      backgroundImage: `url(${ICON_DROPDOWN})`,
      backgroundSize: '24px',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'center',
      transitionTimingFunction: EASING_FUNCTION,
      filter: 'var(--icon-filter, none)', // 通过 CSS 变量控制深色主题
    };

    return (
      <div
        className={cn(containerClasses, 'dropdown')}
        onMouseDown={this.onMouseDown}
        style={style}
      >
        <div className={labelClasses}>{label}</div>
        <div 
          className={iconClasses}
          style={iconStyle}
        />
        {expanded && (
          <ContextMenu
            key={`contextmenu-${this.instanceId}`} // 强制每个 Dropdown 有独立的 ContextMenu 实例
            style={{
              top: 32,
              width: '100%',
              zIndex: 10000 + (this.props.priority || 0)
            }}
            visible={true} // 只有在展开时才渲染，所以总是可见
          >
            {React.Children.map(children, (child) => {
              const { props } = child;

              return React.cloneElement(child, {
                selected: value === props.value,
                onClick: this.onItemClick(props.value),
                onMouseDown: this.onItemMouseDown,
              });
            })}
          </ContextMenu>
        )}
      </div>
    );
  }
}
