// ContextMenu 组件的 Tailwind 版本 - 从 Dropdown 中提取
import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { DIALOG_EASING } from '@mario-ai/shared';

// ContextMenu 组件
interface ContextMenuProps {
  visible: boolean;
  children: React.ReactNode;
  style?: React.CSSProperties;
  bigger?: boolean;
  translucent?: boolean;
  className?: string;
}

export const ContextMenu = React.forwardRef<HTMLDivElement, ContextMenuProps>(({
  visible,
  children,
  style,
  bigger = false,
  translucent = false,
  className
}, ref) => {
  const classes = cn(
    // 基础样式 - 使用 fixed 定位确保最高层级
    'outline-none fixed cursor-default',
    'backdrop-blur-[10px] rounded-[10px] shadow-dialog',
    // 动画效果
    'transition-all duration-[350ms]',
    // 背景色 - 根据 translucent 属性
    translucent ? 'bg-mario-dropdown-translucent' : 'bg-mario-dropdown',
    className
  );

  const dynamicStyle: React.CSSProperties = {
    padding: `${bigger ? 8 : 4}px 0`,
    width: style?.width || '150px',
    transitionTimingFunction: visible ? DIALOG_EASING : 'none',
    // 恢复原始设计：简单的 absolute 定位
    position: 'absolute',
    zIndex: 9999,
    // 原始的 transform 逻辑
    transform: `translate3d(0px, ${visible ? 0 : -10}px, 0px)`,
    opacity: visible ? 1 : 0,
    pointerEvents: visible ? 'inherit' : 'none',
    backfaceVisibility: 'hidden',
    ...style
  };

  return (
    <div ref={ref} className={classes} style={dynamicStyle}>
      {children}
    </div>
  );
});

ContextMenu.displayName = 'ContextMenu';

// ContextMenuSeparator 组件
interface ContextMenuSeparatorProps {
  bigger?: boolean;
  className?: string;
}

export const ContextMenuSeparator = React.forwardRef<HTMLDivElement, ContextMenuSeparatorProps>(({
  bigger = false,
  className
}, ref) => {
  const classes = cn(
    'h-px w-full bg-mario-dropdown-separator',
    bigger ? 'my-2' : 'my-1',
    className
  );

  return <div ref={ref} className={classes} />;
});

ContextMenuSeparator.displayName = 'ContextMenuSeparator';

// ContextMenuRow 组件
interface ContextMenuRowProps {
  children: React.ReactNode;
  className?: string;
}

export const ContextMenuRow = React.forwardRef<HTMLDivElement, ContextMenuRowProps>(({
  children,
  className
}, ref) => {
  const classes = cn(
    'mx-5 flex items-center',
    className
  );

  return <div ref={ref} className={classes}>{children}</div>;
});

ContextMenuRow.displayName = 'ContextMenuRow';

// ContextMenuItem 组件
export interface ContextMenuItemProps {
  icon?: string;
  selected?: boolean;
  bigger?: boolean;
  visible?: boolean;
  iconSize?: number;
  disabled?: boolean;
  onClick?: () => void;
  onMouseDown?: (e: React.MouseEvent<any>) => void;
  children?: React.ReactNode;
  className?: string;
}

export const ContextMenuItem = React.forwardRef<HTMLDivElement, ContextMenuItemProps>(({
  icon,
  selected = false,
  bigger = false,
  visible = true,
  iconSize = 18,
  disabled = false,
  onClick,
  onMouseDown,
  children,
  className,
  ...props
}, ref) => {
  const classes = cn(
    // 基础样式
    'relative font-normal flex items-center',
    // 尺寸和间距
    bigger ? 'text-sm py-3 px-5' : 'text-[13px] py-[10px] px-3',
    // 可见性
    visible ? 'flex' : 'hidden',
    // 禁用状态 - 使用pointer-events-none，与原始工程保持一致
    disabled ? 'opacity-38 pointer-events-none' : 'opacity-100 pointer-events-auto',
    // 选中状态背景
    selected
      ? 'bg-black bg-opacity-10 dark:bg-white dark:bg-opacity-15'
      : 'bg-transparent',
    // Hover 效果
    !disabled && (
      selected
        ? 'hover:bg-black hover:bg-opacity-10 dark:hover:bg-white dark:hover:bg-opacity-15'
        : 'hover:bg-black hover:bg-opacity-6 dark:hover:bg-white dark:hover:bg-opacity-8'
    ),
    className
  );

  const iconStyle: React.CSSProperties = icon ? {
    paddingLeft: `${16 + iconSize + 12}px`,
  } : {};

  const iconBeforeStyle: React.CSSProperties = icon ? {
    content: '""',
    position: 'absolute',
    left: '16px',
    width: `${iconSize}px`,
    height: `${iconSize}px`,
    backgroundImage: `url(${icon})`,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    opacity: 0.54,
    filter: 'var(--icon-filter, none)', // 通过 CSS 变量控制深色主题
    WebkitBackfaceVisibility: 'hidden',
    WebkitTransform: 'translateZ(0) scale(1.0, 1.0)',
  } : {};

  const handleClick = (e: React.MouseEvent) => {
    console.log('🔥 [CLICK-CHAIN] ContextMenuItem clicked, children:', children);
    if (onClick) {
      onClick();
    }
  };

  return (
    <div
      ref={ref}
      className={classes}
      style={{ ...iconStyle }}
      onClick={handleClick}
      onMouseDown={onMouseDown}
      {...props}
    >
      {icon && (
        <div
          className="absolute"
          style={iconBeforeStyle}
        />
      )}
      {children}
    </div>
  );
});

ContextMenuItem.displayName = 'ContextMenuItem';

// 默认属性
ContextMenuItem.defaultProps = {
  iconSize: 18,
};
