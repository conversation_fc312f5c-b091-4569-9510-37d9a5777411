import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { BLUE_500 } from '@mario-ai/shared';

interface Props {
  color?: string;
  clickable?: boolean;
  value?: boolean;
  dense?: boolean;
  onClick?: () => void;
}

export const Switch = ({ 
  color = BLUE_500, 
  clickable = true, 
  value = false, 
  onClick, 
  dense = false 
}: Props) => {
  // Switch 容器样式
  const switchClasses = cn(
    'rounded-[32px] relative overflow-hidden transition-colors duration-150',
    // 尺寸根据 dense 属性
    dense ? 'w-8 h-4' : 'w-9 h-[18px]',
    // 光标样式
    clickable ? 'cursor-pointer' : 'cursor-default'
  );

  // Switch 动态样式
  const switchStyle: React.CSSProperties = {
    backgroundColor: value ? color : 'var(--mario-switch-bg)', // 修正：使用正确的CSS变量名
  };

  // 调试信息
  console.log('🔧 [SWITCH] Debug info:', {
    value,
    color,
    backgroundColor: switchStyle.backgroundColor,
    computedBg: value ? color : 'var(--mario-switch-bg)'
  });

  // 伪元素 hover 效果样式
  const afterClasses = cn(
    'absolute inset-0 z-[2] transition-colors duration-150',
    // Hover 效果 - 修正：浅色主题用黑色，深色主题用白色
    'hover:bg-black hover:bg-opacity-6 dark:hover:bg-white dark:hover:bg-opacity-12'
  );

  // Thumb (滑块) 样式
  const thumbClasses = cn(
    'rounded-full absolute z-[3] transition-all duration-150',
    'top-1/2 -translate-y-1/2 bg-white',
    // 尺寸根据 dense 属性
    dense ? 'w-3 h-3' : 'w-[14px] h-[14px]'
  );

  // Thumb 位置计算
  const thumbStyle: React.CSSProperties = {
    left: value 
      ? (dense ? '18px' : '20px')  // 激活状态的位置
      : '2px',                     // 默认位置
  };

  return (
    <div
      className={switchClasses}
      style={switchStyle}
      onClick={clickable ? onClick : undefined}
    >
      {/* Hover 效果层 */}
      <div className={afterClasses} />
      
      {/* 滑块 */}
      <div
        className={thumbClasses}
        style={thumbStyle}
      />
    </div>
  );
};

// 默认属性
(Switch as any).defaultProps = {
  color: BLUE_500,
  defaultValue: false,
};
