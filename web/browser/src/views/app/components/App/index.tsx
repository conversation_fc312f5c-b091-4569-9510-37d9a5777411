import { observer } from 'mobx-react-lite';
import * as React from 'react';

import { cn } from '@browser/utils/tailwind-helpers';
import { Titlebar } from '../Titlebar';
import { Toolbar } from '../Toolbar';
import store from '../../store';
import { UIStyle } from '@browser/core/styles/default-styles';
import { BookmarkBar } from '../BookmarkBar';
import { TailwindThemeManager } from '@browser/core/utils/tailwind-theme-manager';
import { eventUtils } from '@browser/core/utils/platform-lite';
import {
  DEFAULT_TITLEBAR_HEIGHT,
  COMPACT_TITLEBAR_HEIGHT,
  DEFAULT_TAB_MARGIN_TOP,
  COMPACT_TAB_MARGIN_TOP,
  COMPACT_TAB_HEIGHT,
  DEFAULT_TAB_HEIGHT,
} from '@mario-ai/shared';

const onAppLeave = () => {
  store.barHideTimer = setTimeout(function () {
    if (
      Object.keys(store.dialogsVisibility).some(
        (k) => store.dialogsVisibility[k],
      )
    ) {
      onAppLeave();
    } else {
      store.titlebarVisible = false;
    }
  }, 500);
};

const onAppEnter = () => {
  clearTimeout(store.barHideTimer);
};

const onLineEnter = () => {
  store.titlebarVisible = true;
};



const App = observer(() => {
  console.log('[App] Rendering App component');

  // 初始化主题
  React.useEffect(() => {
    console.log('[App] Initializing theme:', store.settings.object.theme, 'themeAuto:', store.settings.object.themeAuto);
    TailwindThemeManager.setThemeWithAuto(store.settings.object.theme, store.settings.object.themeAuto);
  }, []);

  // 监听主题变化
  React.useEffect(() => {
    const currentTheme = store.settings.object.theme;
    const isAuto = store.settings.object.themeAuto;
    console.log('[App] Theme changed to:', currentTheme, 'themeAuto:', isAuto);
    TailwindThemeManager.setThemeWithAuto(currentTheme, isAuto);
  }, [store.settings.object.theme, store.settings.object.themeAuto]);

  // 初始化启动Tab - 确保有初始Tab被创建
  React.useEffect(() => {
    console.log('[App] Initializing startup tabs...');
    store.startupTabs.load().catch(error => {
      console.error('[App] Failed to load startup tabs:', error);
    });
  }, []);

  // 全局点击监听器 - 点击空白处关闭菜单
  React.useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      // 检查菜单是否打开
      if (store.dialogsVisibility['menu']) {
        // 检查点击的元素是否是菜单按钮或其子元素
        const target = e.target as Element;
        const isMenuButton = target?.closest('[data-menu-button]');

        if (!isMenuButton) {
          console.log('[App] Global click detected outside menu button, hiding menu');
          // 关闭菜单
          eventUtils.send(`hide-menu-dialog-${store.windowId}`);
        }
      }
    };

    document.addEventListener('mousedown', handleGlobalClick);

    return () => {
      document.removeEventListener('mousedown', handleGlobalClick);
    };
  }, []);

  // StyledApp 样式 - Tailwind 版本
  const appClasses = cn(
    'flex flex-col bg-white overflow-hidden'
  );

  // Line 样式 - Tailwind 版本
  const lineClasses = cn(
    'h-px w-full z-[100] relative bg-black'
  );

  const appStyle: React.CSSProperties = {
    // 在浏览器环境中，始终显示标题栏，不隐藏
    height: 'auto',
  };



  const lineStyle: React.CSSProperties = {
    // 在浏览器环境中，不需要这个line
    height: 0,
  };

  return (
    <>
      <div
        className={appClasses}
        style={appStyle}
        onMouseOver={store.isFullscreen ? onAppEnter : undefined}
        onMouseLeave={store.isFullscreen ? onAppLeave : undefined}
      >
        <UIStyle />
        <Titlebar />
        {store.settings.object.topBarVariant === 'default' && <Toolbar />}
        <BookmarkBar />
      </div>
      <div
        className={lineClasses}
        style={lineStyle}
        onMouseOver={onLineEnter}
      />
    </>
  );
});

export default App;
