import { observer } from 'mobx-react-lite';
import * as React from 'react';

import { transparency, TOOLBAR_BUTTON_WIDTH, TOOLBAR_BUTTON_HEIGHT } from '@mario-ai/shared';
import { BLUE_500 } from '@mario-ai/shared';
import { Preloader } from '@browser/core/components/Preloader';
import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';

interface Props {
  onClick?: (e?: React.MouseEvent<HTMLDivElement>) => void;
  onMouseDown?: (e?: React.MouseEvent<HTMLDivElement>) => void;
  onMouseUp?: (e?: React.MouseEvent<HTMLDivElement>) => void;
  onContextMenu?: (e?: React.MouseEvent<HTMLDivElement>) => void;
  size?: number;
  style?: any;
  icon: string;
  divRef?: (ref: HTMLDivElement) => void;
  disabled?: boolean;
  className?: string;
  children?: any;
  opacity?: number;
  autoInvert?: boolean;
  badgeBackground?: string;
  badge?: boolean;
  badgeTextColor?: string;
  badgeText?: string;
  badgeTop?: number;
  badgeRight?: number;
  preloader?: boolean;
  // 支持额外的 data 属性
  [key: string]: any;
  value?: number;
  toggled?: boolean;
  dense?: boolean;
  iconStyle?: any;
  id?: string;
}

export const ToolbarButton = observer(
  ({
    icon,
    onClick,
    onMouseDown,
    size = 20,
    disabled = false,
    className,
    divRef,
    children,
    opacity = transparency.icons.active,
    autoInvert = true,
    style,
    badgeText,
    badgeBackground = BLUE_500,
    badge = false,
    badgeTextColor = 'white',
    badgeTop = 4,
    badgeRight = 4,
    value,
    preloader = false,
    onContextMenu,
    onMouseUp,
    toggled = false,
    dense = false,
    iconStyle,
    id,
    ...restProps // 接收额外的 props
  }: Props) => {
    style = { ...style };

    // Button 样式 - Tailwind 版本
    const buttonClasses = cn(
      'relative transition-colors mx-0.5 flex items-center justify-center',
      // 基础尺寸
      dense ? 'rounded-sm h-[26px] min-w-[34px]' : `rounded h-[${TOOLBAR_BUTTON_HEIGHT}px] min-w-[${TOOLBAR_BUTTON_WIDTH}px]`,
      // 禁用状态
      disabled ? 'pointer-events-none' : 'pointer-events-auto',
      // webkit-app-region
      disabled ? '[&]:[-webkit-app-region:drag]' : '[&]:[-webkit-app-region:no-drag]',
      // 背景色根据状态和主题
      toggled
        ? store.theme['toolbar.lightForeground']
          ? 'bg-white/12'
          : 'bg-black/10'
        : 'bg-transparent',
      // hover 效果 (只在非 toggled 状态下)
      !toggled && !disabled && (store.theme['toolbar.lightForeground']
        ? 'hover:bg-white/8'
        : 'hover:bg-black/6'),
      // active 效果
      !disabled && (store.theme['toolbar.lightForeground']
        ? 'active:bg-white/12'
        : 'active:bg-black/10'),
      className
    );

    // Icon 样式 - Tailwind 版本
    const iconClasses = cn(
      'w-full h-full transition-[background-image] duration-150',
      'bg-center bg-no-repeat bg-contain',
      // 禁用状态透明度
      disabled ? 'opacity-25' : '',
      // 自动反色过滤器
      autoInvert && store.theme['toolbar.lightForeground'] ? 'filter-mario-icon' : ''
    );

    const iconStyle2 = {
      backgroundImage: `url(${icon})`,
      width: `${size}px`,
      height: `${size}px`,
      opacity: disabled ? 0.25 : opacity,
      ...iconStyle
    };


    // Badge 样式 - Tailwind 版本
    const badgeClasses = cn(
      'absolute px-1 py-0.5 rounded-lg min-h-[6px] pointer-events-none z-[5] text-[8px]'
    );

    const badgeStyle = {
      backgroundColor: badgeBackground,
      color: badgeTextColor,
      right: `${badgeRight}px`,
      top: `${badgeTop}px`,
    };

    // PreloaderBg 样式 - Tailwind 版本
    const preloaderBgClasses = cn(
      'w-8 h-8 pointer-events-none absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full',
      'border-3',
      store.theme['toolbar.lightForeground'] ? 'border-white/10' : 'border-black/6'
    );

    return (
      <div
        id={id}
        onClick={onClick}
        onContextMenu={onContextMenu}
        onMouseDown={onMouseDown}
        onMouseUp={onMouseUp}
        className={buttonClasses}
        style={{ borderRadius: 99999, ...style }}
        ref={divRef}
        {...restProps} // 传递额外的 props
      >
        <div
          className={iconClasses}
          style={iconStyle2}
        />
        {badge && (
          <div
            className={badgeClasses}
            style={badgeStyle}
          >
            {badgeText}
          </div>
        )}
        {preloader && value > 0 && (
          <>
            <div className={preloaderBgClasses}></div>
            <Preloader
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: `translate(-50%, -50%)`,
                pointerEvents: 'none',
              }}
              thickness={3}
              size={36}
              value={value}
            />
          </>
        )}
        {children}
      </div>
    );
  },
);

// 默认属性已经在函数参数中设置
