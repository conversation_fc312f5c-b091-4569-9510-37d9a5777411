// Settings 页面共享的 Tailwind 样式组件
import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { ICON_BACK } from '@mario-ai/shared';

// Title 组件 - 设置项标题
interface TitleProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

export const Title = React.forwardRef<HTMLDivElement, TitleProps>(
  ({ children, className, ...props }, ref) => {
    const classes = cn(
      'text-sm font-roboto font-medium', // font-size: 14px, robotoMedium
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    );
  }
);

Title.displayName = 'Title';

// Header 组件 - 页面标题
interface HeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

export const Header = React.forwardRef<HTMLDivElement, HeaderProps>(
  ({ children, className, ...props }, ref) => {
    const classes = cn(
      'mt-1 mb-4 text-xl flex items-center font-roboto font-light', // margin-top: 4px, margin-bottom: 16px, font-size: 20px
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    );
  }
);

Header.displayName = 'Header';

// Row 组件 - 设置项行
interface RowProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  theme?: any;
}

export const Row = React.forwardRef<HTMLDivElement, RowProps>(
  ({ children, className, theme, ...props }, ref) => {
    const classes = cn(
      'w-full flex items-center min-h-12 cursor-pointer',
      // 边框样式 - 最后一个元素没有边框
      'border-b last:border-b-0',
      // 根据主题设置边框颜色
      theme?.['pages.lightForeground']
        ? 'border-white border-opacity-12'
        : 'border-black border-opacity-12',
      className
    );

    const handleClick = (e: React.MouseEvent) => {
      console.log('🔥 [CLICK-CHAIN] Row clicked, target:', e.target);
      console.log('🔥 [CLICK-CHAIN] Row clicked, currentTarget:', e.currentTarget);
      // 如果有原始的 onClick，调用它
      if (props.onClick) {
        props.onClick(e);
      }
    };

    return (
      <div ref={ref} className={classes} {...props} onClick={handleClick}>
        {children}
      </div>
    );
  }
);

Row.displayName = 'Row';

// Control 组件 - 控件容器
interface ControlProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

export const Control = React.forwardRef<HTMLDivElement, ControlProps>(
  ({ children, className, ...props }, ref) => {
    const classes = cn(
      'ml-auto', // margin-left: auto
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    );
  }
);

Control.displayName = 'Control';

// SecondaryText 组件 - 次要文本
interface SecondaryTextProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

export const SecondaryText = React.forwardRef<HTMLDivElement, SecondaryTextProps>(
  ({ children, className, ...props }, ref) => {
    const classes = cn(
      'opacity-54 font-normal mt-1 text-xs', // opacity: 0.54, font-weight: 400, margin-top: 4px, font-size: 12px
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    );
  }
);

SecondaryText.displayName = 'SecondaryText';

// IconButton 组件 - 图标按钮
interface IconButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  className?: string;
  theme?: any;
}

export const IconButton = React.forwardRef<HTMLDivElement, IconButtonProps>(
  ({ children, className, theme, ...props }, ref) => {
    const classes = cn(
      'rounded cursor-pointer w-[38px] h-[38px] opacity-70',
      'bg-center bg-no-repeat bg-contain',
      'hover:bg-black hover:bg-opacity-8',
      // 根据主题设置图标过滤器
      theme?.['pages.lightForeground'] ? 'invert' : '',
      className
    );

    return (
      <div ref={ref} className={classes} {...props}>
        {children}
      </div>
    );
  }
);

IconButton.displayName = 'IconButton';

// Back 组件 - 返回按钮
interface BackProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  theme?: any;
}

export const Back = React.forwardRef<HTMLDivElement, BackProps>(
  ({ className, theme, ...props }, ref) => {
    const classes = cn(
      'rounded cursor-pointer w-[38px] h-[38px] opacity-70',
      'bg-center bg-no-repeat bg-contain',
      'hover:bg-black hover:bg-opacity-8',
      'absolute -left-12', // position: absolute, left: -48px
      // 根据主题设置图标过滤器
      theme?.['pages.lightForeground'] ? 'invert' : '',
      className
    );

    const style: React.CSSProperties = {
      backgroundImage: `url(${ICON_BACK})`,
    };

    return (
      <div ref={ref} className={classes} style={style} {...props} />
    );
  }
);

Back.displayName = 'Back';
