
import { Dropdown } from '@browser/core/components/Dropdown';
import { Switch } from '@browser/core/components/Switch';
import { Title, Row, Control, Header } from '../shared-styles';
import store from '../../store';
import { onSwitchChange } from '../../utils';
import { observer } from 'mobx-react-lite';
import { action } from 'mobx';
import type { TopBarVariant } from '@mario-ai/shared';

const onThemeChange = action((value: string) => {
  console.log('🎨 [THEME-CHANGE] Called with value:', value);

  if (value === 'auto') {
    store.settings.themeAuto = true;
  } else {
    store.settings.themeAuto = false;
    store.settings.theme = value;
  }

  store.save();
});

const ThemeVariant = observer(() => {
  const defaultValue = store.settings.theme;
  const currentValue = store.settings.themeAuto ? 'auto' : defaultValue;

  return (
    <Row>
      <Title>主题颜色</Title>
      <Control>
        <Dropdown
          key="theme-dropdown"
          defaultValue={currentValue}
          onChange={onThemeChange}
          priority={10} // 给主题菜单更高的优先级
        >
          <Dropdown.Item key="theme-auto" value="auto">自动</Dropdown.Item>
          <Dropdown.Item key="theme-light" value="wexond-light">浅色</Dropdown.Item>
          <Dropdown.Item key="theme-dark" value="wexond-dark">深色</Dropdown.Item>
        </Dropdown>
      </Control>
    </Row>
  );
});



const onTopBarChange = action((value: TopBarVariant) => {
  console.log('📊 [TOPBAR-CHANGE] Called with value:', value);

  store.settings.topBarVariant = value;
  store.save();
});

const TopBarVariant = observer(() => {
  return (
    <Row>
      <Title>顶部标题栏</Title>
      <Control>
        <Dropdown
          key="topbar-dropdown"
          defaultValue={store.settings.topBarVariant}
          onChange={onTopBarChange}
        >
          <Dropdown.Item key="topbar-default" value="default">分离显示</Dropdown.Item>
          <Dropdown.Item key="topbar-compact" value="compact">融合显示</Dropdown.Item>
        </Dropdown>
      </Control>
    </Row>
  );
});



const WarnQuit = observer(() => {
  const { warnOnQuit } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('warnOnQuit')}>
      <Title>当关闭多个标签时显示警告弹窗</Title>
      <Control>
        <Switch value={warnOnQuit} />
      </Control>
    </Row>
  );
});

const MenuAnimations = observer(() => {
  const { animations } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('animations')}>
      <Title>菜单动画</Title>
      <Control>
        <Switch value={animations} />
      </Control>
    </Row>
  );
});

const BookmarksBar = observer(() => {
  const { bookmarksBar } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('bookmarksBar')}>
      <Title>显示书签栏</Title>
      <Control>
        <Switch value={bookmarksBar} />
      </Control>
    </Row>
  );
});

export const Appearance = observer(() => {
  return (
    <>
      <Header>个性化</Header>
      {/* <MenuAnimations /> */}
      <BookmarksBar />
      <WarnQuit />
      <ThemeVariant />
      <TopBarVariant />
    </>
  );
});
