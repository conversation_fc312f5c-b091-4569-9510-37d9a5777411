import * as React from 'react';

import { Dropdown } from '@browser/core/components/Dropdown';
import { ContextMenu, ContextMenuItem } from '@browser/core/components/ContextMenu';
import { Switch } from '@browser/core/components/Switch';
import { Title, Row, Control, Header } from '../shared-styles';
import store from '../../store';
import { onSwitchChange } from '../../utils';
import { observer } from 'mobx-react-lite';
import { action } from 'mobx';
import { TopBarVariant } from '@mario-ai/shared';

const onThemeChange = action((value: string) => {
  console.log('[Appearance] Theme change triggered:', value);
  console.log('[Appearance] Current settings before change:', {
    theme: store.settings.theme,
    themeAuto: store.settings.themeAuto
  });

  if (value === 'auto') {
    console.log('[Appearance] Setting themeAuto to true');
    store.settings.themeAuto = true;
  } else {
    console.log('[Appearance] Setting themeAuto to false, theme to:', value);
    store.settings.themeAuto = false;
    store.settings.theme = value;
  }

  console.log('[Appearance] Settings after change:', {
    theme: store.settings.theme,
    themeAuto: store.settings.themeAuto
  });

  // 保存设置 - 这会触发主题更新
  console.log('[Appearance] Calling store.save()');
  store.save();
});

// 简单的主题选择器组件，避免Dropdown组件的问题
const SimpleThemeSelector = observer(() => {
  const [expanded, setExpanded] = React.useState(false);
  const currentValue = store.settings.themeAuto ? 'auto' : store.settings.theme;

  const options = [
    { value: 'auto', label: '自动' },
    { value: 'wexond-light', label: '浅色' },
    { value: 'wexond-dark', label: '深色' }
  ];

  const currentLabel = options.find(opt => opt.value === currentValue)?.label || '未知';

  const handleSelect = (value: string) => {
    console.log('[SimpleThemeSelector] Selected:', value);
    onThemeChange(value);
    setExpanded(false);
  };

  // 点击外部关闭菜单
  React.useEffect(() => {
    if (expanded) {
      const handleClickOutside = () => setExpanded(false);
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [expanded]);

  return (
    <div className="relative">
      <div
        className="h-8 min-w-[200px] relative rounded cursor-pointer select-none flex items-center transition-colors duration-200 bg-mario-control hover:bg-mario-control-hover"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="text-[13px] ml-2 pointer-events-none text-mario-control-value">
          {currentLabel}
        </div>
        <div className="w-6 h-6 ml-auto mr-[2px] transition-transform duration-200 bg-center bg-no-repeat bg-contain"
             style={{
               backgroundImage: `url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTBMMTIgMTVMMTcgMTAiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K)`,
               transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)'
             }}
        />
      </div>

      {expanded && (
        <ContextMenu style={{ top: 32, width: '100%' }} visible={true}>
          {options.map(option => (
            <ContextMenuItem
              key={option.value}
              selected={currentValue === option.value}
              onClick={() => handleSelect(option.value)}
              bigger
            >
              {option.label}
            </ContextMenuItem>
          ))}
        </ContextMenu>
      )}
    </div>
  );
});

const ThemeVariant = observer(() => {
  console.log('[ThemeVariant] Rendering');

  return (
    <Row theme={store.theme}>
      <Title>主题颜色</Title>
      <Control>
        <SimpleThemeSelector />
      </Control>
    </Row>
  );
});

const onTopBarChange = action((value: TopBarVariant) => {
  console.log('[Appearance] onTopBarChange called with:', value);
  store.settings.topBarVariant = value;
  store.save();
});

// 简单的顶部标题栏选择器组件
const SimpleTopBarSelector = observer(() => {
  const [expanded, setExpanded] = React.useState(false);
  const currentValue = store.settings.topBarVariant;

  const options = [
    { value: 'default', label: '分离显示' },
    { value: 'compact', label: '融合显示' }
  ];

  const currentLabel = options.find(opt => opt.value === currentValue)?.label || '未知';

  const handleSelect = (value: TopBarVariant) => {
    console.log('[SimpleTopBarSelector] Selected:', value);
    onTopBarChange(value);
    setExpanded(false);
  };

  // 点击外部关闭菜单
  React.useEffect(() => {
    if (expanded) {
      const handleClickOutside = () => setExpanded(false);
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [expanded]);

  return (
    <div className="relative">
      <div
        className="h-8 min-w-[200px] relative rounded cursor-pointer select-none flex items-center transition-colors duration-200 bg-mario-control hover:bg-mario-control-hover"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="text-[13px] ml-2 pointer-events-none text-mario-control-value">
          {currentLabel}
        </div>
        <div className="w-6 h-6 ml-auto mr-[2px] transition-transform duration-200 bg-center bg-no-repeat bg-contain"
             style={{
               backgroundImage: `url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTBMMTIgMTVMMTcgMTAiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K)`,
               transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)'
             }}
        />
      </div>

      {expanded && (
        <ContextMenu style={{ top: 32, width: '100%' }} visible={true}>
          {options.map(option => (
            <ContextMenuItem
              key={option.value}
              selected={currentValue === option.value}
              onClick={() => handleSelect(option.value as TopBarVariant)}
              bigger
            >
              {option.label}
            </ContextMenuItem>
          ))}
        </ContextMenu>
      )}
    </div>
  );
});

const TopBarVariantSection = observer(() => {
  console.log('[TopBarVariantSection] Rendering');

  return (
    <Row theme={store.theme}>
      <Title>顶部标题栏</Title>
      <Control>
        <SimpleTopBarSelector />
      </Control>
    </Row>
  );
});

const WarnQuit = observer(() => {
  const { warnOnQuit } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('warnOnQuit')}>
      <Title>当关闭多个标签时显示警告弹窗</Title>
      <Control>
        <Switch value={warnOnQuit} />
      </Control>
    </Row>
  );
});

const MenuAnimations = observer(() => {
  const { animations } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('animations')}>
      <Title>菜单动画</Title>
      <Control>
        <Switch value={animations} />
      </Control>
    </Row>
  );
});

const BookmarksBar = observer(() => {
  const { bookmarksBar } = store.settings;

  return (
    <Row theme={store.theme} onClick={onSwitchChange('bookmarksBar')}>
      <Title>显示书签栏</Title>
      <Control>
        <Switch value={bookmarksBar} />
      </Control>
    </Row>
  );
});

export const Appearance = observer(() => {
  return (
    <>
      <Header>个性化</Header>
      {/* <MenuAnimations /> */}
      <BookmarksBar />
      <WarnQuit />
      <ThemeVariant />
      <TopBarVariantSection />
    </>
  );
});
